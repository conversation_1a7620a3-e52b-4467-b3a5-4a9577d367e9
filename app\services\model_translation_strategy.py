"""
Model list translation strategy for converting between Ollama and OpenAI API formats.

This module implements transparent passthrough translation logic for model list responses,
converting from MedusaXD/OpenAI format to Ollama format while preserving original model names.

The strategy maintains complete transparency by using backend model IDs directly as Ollama model names,
ensuring all backend models are exposed with their original identities.
"""

import logging
from datetime import datetime
from typing import List

from app.api.schemas.ollama_schemas import (
    OllamaModel,
    OllamaTagsResponse,
)
from app.api.schemas.medusa_schemas import (
    MedusaModel,
    MedusaModelListResponse,
)
from app.core.config import Settings

logger = logging.getLogger(__name__)


class ModelListTranslationStrategy:
    """
    Translation strategy for model lists.
    
    Handles translation between MedusaXD/OpenAI and Ollama model list formats.
    """
    
    def __init__(self, settings: Settings):
        """
        Initialize the model list translation strategy.
        
        Args:
            settings: Application settings containing model mappings
        """
        self.settings = settings
    
    def translate_model_list(self, medusa_response: MedusaModelListResponse) -> OllamaTagsResponse:
        """
        Translate a MedusaXD model list response to Ollama format with transparent passthrough.

        This method preserves all original model names from the backend API without any
        filtering, mapping, or transformation. All backend models are exposed with their
        original identities to maintain complete transparency.

        Args:
            medusa_response: The MedusaXD model list response

        Returns:
            OllamaTagsResponse: The translated response in Ollama format with original model names
        """
        logger.debug(f"Translating model list with {len(medusa_response.data)} models (transparent passthrough)")

        ollama_models = []
        for medusa_model in medusa_response.data:
            ollama_model = self._translate_single_model(medusa_model)
            ollama_models.append(ollama_model)

        ollama_response = OllamaTagsResponse(models=ollama_models)

        logger.debug(f"Translated model list: {len(ollama_models)} models with original names preserved")
        return ollama_response
    
    def _translate_single_model(self, medusa_model: MedusaModel) -> OllamaModel:
        """
        Translate a single MedusaXD model to Ollama format with transparent passthrough.

        This method preserves the original backend model ID as the Ollama model name,
        ensuring complete transparency and no transformation of model identities.

        Args:
            medusa_model: The MedusaXD model

        Returns:
            OllamaModel: The translated model in Ollama format with original name preserved
        """
        # Convert Unix timestamp to datetime
        modified_at = datetime.fromtimestamp(medusa_model.created)

        # TRANSPARENT PASSTHROUGH: Use backend model ID directly as the Ollama model name
        # This ensures all backend models are exposed with their original identities
        model_name = medusa_model.id

        # Default size since backend doesn't provide this information
        # In a real implementation, this could be estimated or configured
        model_size = 0

        # Generate a digest based on the model name for consistency
        # In a real implementation, this would be the actual model file digest
        import hashlib
        digest = hashlib.sha256(model_name.encode()).hexdigest()

        # Create model details with inferred information
        from app.api.schemas.ollama_schemas import OllamaModelDetails
        details = self._create_model_details(model_name)

        ollama_model = OllamaModel(
            name=model_name,
            modified_at=modified_at,
            size=model_size,
            digest=digest,
            details=details
        )

        logger.debug(f"Using model directly: {model_name}")
        return ollama_model

    def _create_model_details(self, model_name: str) -> "OllamaModelDetails":
        """
        Create model details based on the model name.

        Args:
            model_name: The model name to analyze

        Returns:
            OllamaModelDetails: The model details
        """
        from app.api.schemas.ollama_schemas import OllamaModelDetails

        # Try to extract parameter size from model name
        model_name_lower = model_name.lower()
        parameter_size = "unknown"
        if "7b" in model_name_lower:
            parameter_size = "7B"
        elif "13b" in model_name_lower:
            parameter_size = "13B"
        elif "70b" in model_name_lower:
            parameter_size = "70B"
        elif "3b" in model_name_lower:
            parameter_size = "3B"
        elif "1b" in model_name_lower:
            parameter_size = "1B"
        elif "8b" in model_name_lower:
            parameter_size = "8B"

        # Try to extract quantization level from model name
        quantization_level = "unknown"
        if "q4_0" in model_name_lower:
            quantization_level = "Q4_0"
        elif "q4_1" in model_name_lower:
            quantization_level = "Q4_1"
        elif "q5_0" in model_name_lower:
            quantization_level = "Q5_0"
        elif "q5_1" in model_name_lower:
            quantization_level = "Q5_1"
        elif "q8_0" in model_name_lower:
            quantization_level = "Q8_0"
        elif "fp16" in model_name_lower:
            quantization_level = "F16"
        elif "fp32" in model_name_lower:
            quantization_level = "F32"

        # Determine model family based on name patterns
        family = "llama"  # Default
        families = ["llama"]

        if "mistral" in model_name_lower:
            family = "mistral"
            families = ["mistral"]
        elif "codellama" in model_name_lower:
            family = "llama"
            families = ["llama", "code"]
        elif "gemma" in model_name_lower:
            family = "gemma"
            families = ["gemma"]
        elif "phi" in model_name_lower:
            family = "phi"
            families = ["phi"]
        elif "qwen" in model_name_lower:
            family = "qwen"
            families = ["qwen"]

        return OllamaModelDetails(
            parent_model="",
            format="gguf",
            family=family,
            families=families,
            parameter_size=parameter_size,
            quantization_level=quantization_level
        )
    

