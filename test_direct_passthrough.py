#!/usr/bin/env python3
"""
Test script to verify that all backend models are exposed directly without mapping.
"""

import asyncio
import json
import httpx


async def test_direct_model_passthrough():
    """Test that all backend models are exposed directly to Ollama clients."""
    
    async with httpx.AsyncClient() as client:
        try:
            print("🔍 Testing direct model passthrough...")
            print("="*60)
            
            # Test 1: Get all models from /api/tags
            print("1. Testing /api/tags endpoint...")
            response = await client.get(
                "http://localhost:8000/api/tags",
                timeout=30.0
            )
            
            if response.status_code != 200:
                print(f"❌ /api/tags failed with status {response.status_code}")
                print(response.text)
                return
            
            tags_data = response.json()
            models = tags_data.get("models", [])
            print(f"✅ Found {len(models)} models in /api/tags")
            
            # Show first 10 models with their backend IDs
            print("\n📋 First 10 models (showing backend IDs directly):")
            for i, model in enumerate(models[:10]):
                model_name = model.get('name', 'Unknown')
                print(f"  {i+1:2d}. {model_name}")
            
            if len(models) > 10:
                print(f"  ... and {len(models) - 10} more models")
            
            # Test 2: Test /api/show with a backend model ID
            if models:
                test_model = models[0]['name']
                print(f"\n2. Testing /api/show with backend model ID: '{test_model}'")
                
                show_request = {
                    "model": test_model,
                    "verbose": False
                }
                
                response = await client.post(
                    "http://localhost:8000/api/show",
                    json=show_request,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    show_data = response.json()
                    print("✅ /api/show works with backend model ID")
                    print(f"   Model: {test_model}")
                    print(f"   Details: {show_data.get('details', {})}")
                else:
                    print(f"❌ /api/show failed with status {response.status_code}")
                    print(response.text)
            
            # Test 3: Test chat completion with a backend model ID
            if models:
                test_model = models[0]['name']
                print(f"\n3. Testing /api/chat with backend model ID: '{test_model}'")
                
                chat_request = {
                    "model": test_model,
                    "messages": [
                        {"role": "user", "content": "Hello! Just say 'Hi' back."}
                    ],
                    "stream": False
                }
                
                response = await client.post(
                    "http://localhost:8000/api/chat",
                    json=chat_request,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    chat_data = response.json()
                    message = chat_data.get('message', {})
                    content = message.get('content', '')
                    print("✅ /api/chat works with backend model ID")
                    print(f"   Model: {test_model}")
                    print(f"   Response: {content[:100]}...")
                else:
                    print(f"❌ /api/chat failed with status {response.status_code}")
                    print(response.text)
            
            # Test 4: Verify no model mapping is happening
            print(f"\n4. Verifying direct passthrough (no mapping)...")
            
            # Check if we have models with backend-style names
            backend_style_models = [
                m['name'] for m in models 
                if '/' in m['name'] or '@' in m['name'] or m['name'].startswith('Cloudflare/')
            ]
            
            if backend_style_models:
                print(f"✅ Found {len(backend_style_models)} models with backend-style names")
                print("   Examples:")
                for model in backend_style_models[:5]:
                    print(f"     - {model}")
                print("   This confirms models are passed through directly!")
            else:
                print("⚠️  No backend-style model names found")
            
            # Summary
            print(f"\n" + "="*60)
            print("📊 SUMMARY:")
            print(f"   Total models exposed: {len(models)}")
            print(f"   Backend-style names: {len(backend_style_models)}")
            print(f"   Direct passthrough: {'✅ YES' if backend_style_models else '❌ NO'}")
            
            if len(models) >= 500:
                print("✅ SUCCESS: All ~588 backend models are being exposed directly!")
            else:
                print(f"⚠️  Expected ~588 models, got {len(models)}")
                
        except httpx.ConnectError:
            print("❌ Connection failed. Is the server running on localhost:8000?")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    asyncio.run(test_direct_model_passthrough())
