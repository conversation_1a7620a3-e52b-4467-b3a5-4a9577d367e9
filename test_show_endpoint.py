#!/usr/bin/env python3
"""
Test script for the /api/show endpoint.
"""

import asyncio
import json
import httpx


async def test_show_endpoint():
    """Test the /api/show endpoint."""
    
    # Test data
    test_request = {
        "model": "gpt-4o-mini",
        "verbose": False
    }
    
    async with httpx.AsyncClient() as client:
        try:
            # Test the show endpoint
            print("Testing /api/show endpoint...")
            response = await client.post(
                "http://localhost:8000/api/show",
                json=test_request,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                response_data = response.json()
                print("✅ Success! Response:")
                print(json.dumps(response_data, indent=2))
            else:
                print("❌ Error Response:")
                print(response.text)
                
        except httpx.ConnectError:
            print("❌ Connection failed. Is the server running on localhost:8000?")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    asyncio.run(test_show_endpoint())
