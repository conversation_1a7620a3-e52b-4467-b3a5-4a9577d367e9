# Ollama-to-OpenAI API Bridge

A high-performance transparent translation middleware that enables seamless switching between Ollama and OpenAI-compatible APIs without requiring client-side code changes.

## 🎯 Overview

This bridge service acts as a transparent translation proxy, intercepting Ollama-formatted API calls and translating them to OpenAI-compatible format for backend services, then translating responses back to Ollama format for clients.

**🔍 Transparent Passthrough Architecture**: All backend models are exposed with their original names without any mapping, filtering, or transformation. This ensures complete transparency and allows clients to access all available backend models using their exact identities.

### Key Features

- **Seamless Translation**: Full compatibility between Ollama and OpenAI API formats
- **Streaming Support**: Real-time token-by-token streaming responses
- **Model Mapping**: Configurable mapping between Ollama and backend model names
- **High Performance**: <100ms latency overhead for non-streaming requests
- **Easy Configuration**: Environment-based configuration with validation
- **Comprehensive Testing**: Unit, integration, and end-to-end test coverage

## 🚀 Quick Start

### Prerequisites

- Python 3.11 or higher
- Poetry (for dependency management)
- Access to a MedusaXD-compatible backend API

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ollama-bridge
   ```

2. **Install dependencies**
   ```bash
   poetry install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the application**
   ```bash
   poetry run uvicorn app.main:app --reload
   ```

The API will be available at `http://localhost:8000` with interactive documentation at `http://localhost:8000/docs`.

## ⚙️ Configuration

### Required Environment Variables

- `MEDUSA_BACKEND_URL`: Base URL for the backend API service

### Optional Environment Variables

- `API_TIMEOUT`: Request timeout in seconds (default: 30)
- `MODEL_MAPPINGS`: JSON mapping of Ollama to backend model names
- `LOG_LEVEL`: Logging level (default: INFO)
- `DEBUG`: Enable debug mode (default: false)
- `MAX_CONCURRENT_REQUESTS`: Maximum concurrent backend requests (default: 100)

### Example Configuration

```bash
# Required
MEDUSA_BACKEND_URL=https://api.medusaxd.com

# Optional (Note: MODEL_MAPPINGS not used in transparent passthrough mode)
MODEL_MAPPINGS={"llama3": "gpt-4o-mini", "codellama": "gpt-4"}
LOG_LEVEL=INFO
API_TIMEOUT=30
DEBUG=false
```

## 🔄 Transparent Passthrough Architecture

This bridge implements a **transparent passthrough architecture** that preserves the original identity of all backend models:

### Key Principles
- **No Model Filtering**: All backend models are exposed to clients
- **No Name Mapping**: Backend model names are used directly without transformation
- **Complete Transparency**: Clients see the exact same models available on the backend
- **Original Identities Preserved**: Model names, capabilities, and metadata remain unchanged

### How It Works
1. **Model Discovery**: `/api/tags` endpoint fetches all models from backend `/v1/models`
2. **Direct Passthrough**: Model names from backend API are used directly in Ollama format
3. **Chat Requests**: Client model names are passed directly to backend without mapping
4. **Response Translation**: Only the response format is translated, not the content

This ensures that switching between direct backend access and the Ollama bridge is completely seamless, with no loss of model availability or functionality.

## 📚 API Endpoints

### Health Check
- `GET /health` - Service health status

### Ollama-Compatible Endpoints
- `POST /api/chat` - Chat completions (streaming and non-streaming)
- `GET /api/tags` - List available models (transparent passthrough)

### Direct OpenAI-Compatible Endpoints
- `GET /v1/models` - Direct model list from backend
- `GET /v1/TTI/models` - Text-to-image models
- `POST /v1/images/generations` - Image generation
- `GET /search` - Web search

### Authentication Endpoints (Future)
- `POST /v1/auth/generate-key` - Generate API key
- `GET /v1/auth/validate` - Validate API key

## 🧪 Testing

### Run Tests
```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=app

# Run specific test file
poetry run pytest tests/test_health.py
```

### Test Categories
- **Unit Tests**: Individual component testing
- **Integration Tests**: API endpoint testing
- **End-to-End Tests**: Full system validation

## 🏗️ Development

### Project Structure
```
ollama-bridge/
├── app/                    # Application source code
│   ├── api/               # API layer (routers, schemas)
│   ├── core/              # Core configuration and utilities
│   ├── services/          # Business logic services
│   └── main.py           # Application entry point
├── tests/                 # Test suite
├── docs/                  # Documentation
├── pyproject.toml        # Project configuration
└── README.md             # This file
```

### Code Quality

The project uses several tools to maintain code quality:

- **Ruff**: Linting and code formatting
- **MyPy**: Static type checking
- **Pytest**: Testing framework
- **Pre-commit**: Git hooks for quality checks

### Development Commands

```bash
# Install development dependencies
poetry install --with dev

# Run linting
poetry run ruff check app tests

# Run type checking
poetry run mypy app

# Format code
poetry run ruff format app tests

# Run development server
poetry run uvicorn app.main:app --reload --log-level debug
```

## 🐳 Docker Support

### Build Image
```bash
docker build -t ollama-bridge .
```

### Run Container
```bash
docker run -p 8000:8000 \
  -e MEDUSA_BACKEND_URL=https://api.medusaxd.com \
  ollama-bridge
```

## 📖 Documentation

- **Architecture**: See `BMAD-METHOD/docs/architecture.md`
- **Product Requirements**: See `BMAD-METHOD/docs/prd.md`
- **API Documentation**: Available at `/docs` when running the service

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation in the `docs/` directory
- Review the API documentation at `/docs`
- Open an issue for bugs or feature requests

## 🗺️ Roadmap

- [x] **Phase 1**: Foundation and health check
- [ ] **Phase 2**: Core translation functionality
- [ ] **Phase 3**: Extended API capabilities
- [ ] **Phase 4**: Authentication system
- [ ] **Phase 5**: Production deployment

## 📊 Status

**Current Version**: 0.1.0  
**Status**: Development (Foundation Complete)  
**Next Milestone**: Core Translation Implementation
