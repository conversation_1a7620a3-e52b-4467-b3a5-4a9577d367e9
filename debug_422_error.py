#!/usr/bin/env python3
"""
Debug script to help identify the cause of 422 errors in /api/show endpoint.
"""

import json
import requests
import time

def test_various_request_formats():
    """Test different request formats to identify what's causing 422 errors."""
    
    base_url = "http://localhost:8000"
    
    print("🔍 Testing various request formats to debug 422 errors...\n")
    
    # Test cases that might cause 422 errors
    test_cases = [
        {
            "name": "Valid request with existing model",
            "payload": {"model": "TogetherAI/arcee-ai/coder-large"},
            "expected": 200
        },
        {
            "name": "Request with undefined model",
            "payload": {"model": "undefined"},
            "expected": 422
        },
        {
            "name": "Request with null model",
            "payload": {"model": "null"},
            "expected": 422
        },
        {
            "name": "Request with empty model",
            "payload": {"model": ""},
            "expected": 422
        },
        {
            "name": "Request with missing model field",
            "payload": {"verbose": True},
            "expected": 422
        },
        {
            "name": "Request with numeric model",
            "payload": {"model": 123},
            "expected": 422
        },
        {
            "name": "Request with boolean model",
            "payload": {"model": True},
            "expected": 422
        },
        {
            "name": "Request with array model",
            "payload": {"model": ["test"]},
            "expected": 422
        },
        {
            "name": "Request with object model",
            "payload": {"model": {"name": "test"}},
            "expected": 422
        },
        {
            "name": "Request with verbose as string",
            "payload": {"model": "test", "verbose": "true"},
            "expected": 422
        },
        {
            "name": "Empty request body",
            "payload": {},
            "expected": 422
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. Testing: {test_case['name']}")
        print(f"   Payload: {json.dumps(test_case['payload'])}")
        print(f"   Expected: {test_case['expected']}")
        
        try:
            response = requests.post(
                f"{base_url}/api/show",
                json=test_case["payload"],
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            print(f"   Actual: {response.status_code}")
            
            if response.status_code == test_case["expected"]:
                print("   ✅ Expected result")
            else:
                print("   ❌ Unexpected result")
                
            if response.status_code == 422:
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Raw error: {response.text}")
            elif response.status_code == 200:
                try:
                    data = response.json()
                    if "model_info" in data and "general.architecture" in data["model_info"]:
                        print(f"   ✅ general.architecture: {data['model_info']['general.architecture']}")
                    else:
                        print("   ⚠️ Missing general.architecture")
                except:
                    print("   ⚠️ Invalid JSON response")
                    
        except Exception as e:
            print(f"   ❌ Request failed: {e}")
        
        print()


def test_malformed_json():
    """Test malformed JSON requests."""
    print("🔍 Testing malformed JSON requests...\n")
    
    base_url = "http://localhost:8000"
    
    malformed_cases = [
        {
            "name": "Invalid JSON syntax",
            "data": '{"model": "test"',  # Missing closing brace
            "content_type": "application/json"
        },
        {
            "name": "Non-JSON content type",
            "data": '{"model": "test"}',
            "content_type": "text/plain"
        },
        {
            "name": "Empty body with JSON content type",
            "data": '',
            "content_type": "application/json"
        },
        {
            "name": "URL encoded data",
            "data": 'model=test&verbose=true',
            "content_type": "application/x-www-form-urlencoded"
        }
    ]
    
    for i, test_case in enumerate(malformed_cases, 1):
        print(f"{i}. Testing: {test_case['name']}")
        print(f"   Data: {test_case['data']}")
        print(f"   Content-Type: {test_case['content_type']}")
        
        try:
            response = requests.post(
                f"{base_url}/api/show",
                data=test_case["data"],
                headers={"Content-Type": test_case["content_type"]},
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 422:
                try:
                    error_data = response.json()
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Raw response: {response.text}")
            else:
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Request failed: {e}")
        
        print()


def main():
    """Main function to run debug tests."""
    
    # Check if server is running
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ Server is not running or not healthy")
            return
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server at http://localhost:8000")
        return
    
    print("✅ Server is running\n")
    
    test_various_request_formats()
    test_malformed_json()
    
    print("="*60)
    print("📋 Debug Summary:")
    print("- Check the server logs for detailed validation error information")
    print("- Look for patterns in which requests cause 422 errors")
    print("- The enhanced logging should show the exact request body and headers")
    print("- Pay attention to any 'undefined' or 'null' model names in the logs")


if __name__ == "__main__":
    main()
