"""
Models router for Ollama-compatible model list endpoints.

This module provides endpoints for retrieving available models in Ollama format,
translating from the backend API format.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, Request

from app.api.schemas.ollama_schemas import (
    OllamaTagsResponse,
    OllamaShowRequest,
    OllamaShowResponse,
    OllamaModelDetails,
)
from app.services.backend_client_service import (
    BackendClientService,
    BackendClientError,
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
    get_backend_client,
)
from app.services.translation_service import TranslationService
from app.core.config import Settings, get_settings

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


def get_translation_service(settings: Settings = Depends(get_settings)) -> TranslationService:
    """
    Dependency to get the translation service.

    Args:
        settings: Application settings

    Returns:
        TranslationService: Configured translation service
    """
    return TranslationService(settings)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


def get_translation_service(settings: Settings = Depends(get_settings)) -> TranslationService:
    """
    Dependency to get the translation service.

    Args:
        settings: Application settings

    Returns:
        TranslationService: Configured translation service
    """
    return TranslationService(settings)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


@router.get(
    "/api/tags",
    response_model=OllamaTagsResponse,
    summary="List Available Models",
    description="Get a list of available models in Ollama format",
    responses={
        200: {"description": "Successful model list retrieval"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def list_models(
    translation_service: TranslationService = Depends(get_translation_service),
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> OllamaTagsResponse:
    """
    List available models in Ollama format with transparent passthrough.

    This endpoint retrieves ALL models from the backend API and translates them
    to the Ollama /api/tags format while preserving original model names and
    identities. No filtering, mapping, or transformation is applied to model names.

    Args:
        translation_service: Service for transparent format translation
        backend_client: Client for backend API communication

    Returns:
        OllamaTagsResponse: The complete model list in Ollama format with original names

    Raises:
        HTTPException: For various error conditions (502, 504)
    """
    logger.debug("Model list request received (transparent passthrough mode)")

    try:
        # Get ALL models from backend (no filtering)
        logger.debug("Requesting complete model list from backend")
        medusa_response = await backend_client.get_models()

        # Translate to Ollama format (preserving original names)
        logger.debug("Translating model list to Ollama format with transparent passthrough")
        ollama_response = translation_service.translate_model_list(medusa_response)

        logger.info(f"Model list retrieved successfully: {len(ollama_response.models)} models (all backend models exposed)")
        return ollama_response
        
    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during model list request: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while retrieving model list"
        )
        
    except BackendConnectionError as e:
        logger.error(f"Backend connection error during model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Unable to connect to backend service"
        )
        
    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Backend service error: {e}"
        )
        
    except BackendClientError as e:
        logger.error(f"Backend client error during model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        )
        
    except Exception as e:
        logger.error(f"Unexpected error during model list request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )


@router.post(
    "/api/show",
    response_model=OllamaShowResponse,
    summary="Show Model Information",
    description="Get detailed information about a specific model in Ollama format",
    responses={
        200: {"description": "Successful model information retrieval"},
        404: {"description": "Model not found"},
        422: {"description": "Invalid request format"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def show_model(
    raw_request: Request,
    request: OllamaShowRequest,
    translation_service: TranslationService = Depends(get_translation_service),
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> OllamaShowResponse:
    """
    Show detailed information about a specific model in Ollama format.

    This endpoint retrieves model information from the backend API and formats
    it according to the Ollama /api/show specification for compatibility with
    Ollama clients.

    Args:
        request: The show model request containing model name and options
        translation_service: Service for format translation
        backend_client: Client for backend API communication

    Returns:
        OllamaShowResponse: The model information in Ollama format

    Raises:
        HTTPException: For various error conditions (404, 502, 504)
    """
    # Log request details for debugging
    client_ip = raw_request.client.host if raw_request.client else "unknown"
    user_agent = raw_request.headers.get("user-agent", "unknown")
    logger.info(f"Model show request from {client_ip} (User-Agent: {user_agent})")
    logger.info(f"Model show request received for model: '{request.model}' (verbose: {request.verbose})")

    # Validate model name
    if not request.model or request.model.strip() == "":
        logger.warning(f"Invalid model name received: '{request.model}'")
        raise HTTPException(
            status_code=422,
            detail="Model name cannot be empty"
        )

    if request.model == "undefined" or request.model == "null":
        logger.warning(f"Client sent undefined/null model name: '{request.model}'")
        raise HTTPException(
            status_code=422,
            detail="Invalid model name: model name cannot be 'undefined' or 'null'"
        )

    try:
        # Get model list from backend to find the requested model
        logger.debug("Requesting model list from backend to find model")
        medusa_response = await backend_client.get_models()

        # Find the requested model in the list
        target_model = None
        for model in medusa_response.data:
            if model.id == request.model:
                target_model = model
                break

        if target_model is None:
            logger.warning(f"Model not found: {request.model}")
            raise HTTPException(
                status_code=404,
                detail=f"Model '{request.model}' not found"
            )

        # Create model details from available information
        details = OllamaModelDetails(
            parent_model="",
            format="gguf",
            family="llama",  # Default family
            families=["llama"],
            parameter_size="unknown",
            quantization_level="unknown"
        )

        # Try to extract parameter size from model name if possible
        model_name_lower = request.model.lower()
        if "7b" in model_name_lower:
            details.parameter_size = "7B"
        elif "13b" in model_name_lower:
            details.parameter_size = "13B"
        elif "70b" in model_name_lower:
            details.parameter_size = "70B"
        elif "3b" in model_name_lower:
            details.parameter_size = "3B"

        # Create basic model info - always include essential fields that clients expect
        basic_model_info = {
            "general.architecture": "llama",
            "general.file_type": 2,
            "general.parameter_count": 0,
            "general.quantization_version": 2,
        }

        # Add verbose model info if requested
        if request.verbose:
            # Add additional verbose fields that match real Ollama responses
            verbose_model_info = {
                "llama.attention.head_count": 32,
                "llama.attention.head_count_kv": 8,
                "llama.attention.layer_norm_rms_epsilon": 0.00001,
                "llama.block_count": 32,
                "llama.context_length": 8192,
                "llama.embedding_length": 4096,
                "llama.feed_forward_length": 14336,
                "llama.rope.dimension_count": 128,
                "llama.rope.freq_base": 500000,
                "llama.vocab_size": 128256,
                "tokenizer.ggml.bos_token_id": 128000,
                "tokenizer.ggml.eos_token_id": 128009,
                "tokenizer.ggml.model": "gpt2",
                "tokenizer.ggml.pre": "llama-bpe",
                "tokenizer.ggml.merges": [],
                "tokenizer.ggml.token_type": [],
                "tokenizer.ggml.tokens": []
            }
            model_info = {**basic_model_info, **verbose_model_info}
        else:
            model_info = basic_model_info

        # Create the show response
        show_response = OllamaShowResponse(
            modelfile=f'# Modelfile for {request.model}\nFROM {request.model}',
            parameters="",
            template="{{ .System }}{{ .Prompt }}",
            details=details,
            model_info=model_info,
            capabilities=["completion"]
        )

        logger.info(f"Model information retrieved successfully for: {request.model}")
        return show_response

    except HTTPException:
        # Re-raise HTTP exceptions (like 404)
        raise

    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during model show request: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while retrieving model information"
        )

    except BackendConnectionError as e:
        logger.error(f"Backend connection error during model show request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Unable to connect to backend service"
        )

    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during model show request: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Backend service error: {e}"
        )

    except BackendClientError as e:
        logger.error(f"Backend client error during model show request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        )

    except Exception as e:
        logger.error(f"Unexpected error during model show request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )
