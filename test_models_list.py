#!/usr/bin/env python3
"""
Test script to list available models.
"""

import asyncio
import json
import httpx


async def test_models_list():
    """Test the /api/tags endpoint to see available models."""
    
    async with httpx.AsyncClient() as client:
        try:
            # Test the tags endpoint
            print("Testing /api/tags endpoint...")
            response = await client.get(
                "http://localhost:8000/api/tags",
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print("✅ Success! Available models:")
                models = response_data.get("models", [])
                for i, model in enumerate(models[:5]):  # Show first 5 models
                    print(f"  {i+1}. {model.get('name', 'Unknown')}")
                
                if models:
                    print(f"\nTotal models: {len(models)}")
                    return models[0].get('name')  # Return first model name for testing
                else:
                    print("No models found")
                    return None
            else:
                print("❌ Error Response:")
                print(response.text)
                return None
                
        except httpx.ConnectError:
            print("❌ Connection failed. Is the server running on localhost:8000?")
            return None
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return None


async def test_show_with_real_model():
    """Test the show endpoint with a real model."""
    
    # First get a real model name
    model_name = await test_models_list()
    
    if not model_name:
        print("No models available to test with")
        return
    
    print(f"\n" + "="*50)
    print(f"Testing /api/show with model: {model_name}")
    print("="*50)
    
    test_request = {
        "model": model_name,
        "verbose": True
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:8000/api/show",
                json=test_request,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print("✅ Success! Model details:")
                print(json.dumps(response_data, indent=2))
            else:
                print("❌ Error Response:")
                print(response.text)
                
        except Exception as e:
            print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    asyncio.run(test_show_with_real_model())
